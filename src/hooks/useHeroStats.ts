import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

interface HeroStats {
  id: string;
  students_placed: number;
  countries: number;
  success_rate: number;
  years_experience: number;
}

const useHeroStats = () => {
  const [stats, setStats] = useState<HeroStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHeroStats = async () => {
      try {
        const { data, error: supabaseError } = await supabase
          .from('hero_stats')
          .select('*')
          .single();

        if (supabaseError) {
          throw supabaseError;
        }

        setStats(data as HeroStats);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchHeroStats();
  }, []);

  return { stats, loading, error };
};

export default useHeroStats;