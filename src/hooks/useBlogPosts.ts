import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  author: string;
  published_at: string;
  image_url: string;
  created_at: string;
  updated_at: string;
}

export const useBlogPosts = (searchQuery: string = '') => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        setError(null);

        let query = supabase
          .from('blog_posts')
          .select('*')
          .order('published_at', { ascending: false });

        if (searchQuery) {
          query = query.ilike('title', `%${searchQuery}%`);
        }

        const { data, error: supabaseError } = await query;

        if (supabaseError) {
          throw new Error(supabaseError.message);
        }

        setPosts(data as BlogPost[]);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred while fetching blog posts');
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, [searchQuery]);

  return { posts, loading, error };
};