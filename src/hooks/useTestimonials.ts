import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

interface Testimonial {
  id: string;
  created_at: string;
  name: string;
  destination: string;
  university: string;
  course: string;
  image: string;
  rating: number;
  text: string;
}

const useTestimonials = () => {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const { data, error } = await supabase
          .from('testimonials')
          .select('*');

        if (error) {
          throw error;
        }

        setTestimonials(data as Testimonial[]);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  return { testimonials, loading, error };
};

export default useTestimonials;