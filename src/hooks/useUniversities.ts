import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

interface University {
  id: string;
  name: string;
  country_id: string;
  description: string;
  website_url: string;
  logo_url?: string;
  // Add other fields as per your 'universities' table schema
}

const useUniversities = (countryId?: string) => {
  const [universities, setUniversities] = useState<University[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUniversities = async () => {
      try {
        let query = supabase.from('universities').select('*');

        if (countryId) {
          query = query.eq('country_id', countryId);
        }

        const { data, error: supabaseError } = await query;

        if (supabaseError) {
          throw supabaseError;
        }

        setUniversities(data as University[]);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUniversities();
  }, [countryId]);

  return { universities, loading, error };
};

export default useUniversities;