@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Enhanced Color Palette for Education Theme */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Primary: Professional Blue */
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --primary-light: 221 83% 65%;
    --primary-dark: 221 83% 40%;

    /* Secondary: Warm Orange */
    --secondary: 25 95% 53%;
    --secondary-foreground: 0 0% 100%;
    --secondary-light: 25 95% 65%;
    --secondary-dark: 25 95% 40%;

    /* Accent: Success Green */
    --accent: 142 76% 36%;
    --accent-foreground: 210 40% 98%;
    --accent-light: 142 76% 50%;

    /* Neutral Grays - Enhanced Contrast */
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 35%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221 83% 53%;

    /* Text Colors for Better Contrast */
    --text-primary: 222.2 84% 4.9%;
    --text-secondary: 215.4 16.3% 25%;
    --text-muted: 215.4 16.3% 40%;
    --text-light: 215.4 16.3% 60%;

    /* Status Colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;
    --info: 199 89% 48%;
    --info-foreground: 210 40% 98%;

    /* Enhanced Radius */
    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;

    /* Sidebar Variables */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 25 95% 60%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 142 76% 45%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    @apply font-semibold leading-tight tracking-tight;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  h4 {
    @apply text-xl md:text-2xl;
  }

  h5 {
    @apply text-lg md:text-xl;
  }

  h6 {
    @apply text-base md:text-lg;
  }
}

@layer components {
  /* Enhanced Button Styles */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium px-6 py-3 rounded-lg shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium px-6 py-3 rounded-lg shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200;
  }

  .btn-outline {
    @apply border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-medium px-6 py-3 rounded-lg transition-all duration-200;
  }

  /* Enhanced Card Styles */
  .card-elevated {
    @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1;
  }

  .card-gradient {
    @apply bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300;
  }

  /* Glass Effect */
  .glass {
    @apply bg-white/80 backdrop-blur-md border border-white/20 shadow-lg;
  }

  /* Gradient Text */
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-orange-500 bg-clip-text text-transparent;
  }

  /* Enhanced Input Styles */
  .input-enhanced {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white;
  }

  /* Loading Animation */
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Hover Effects */
  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-2 hover:shadow-xl;
  }

  .hover-scale {
    @apply transition-transform duration-300 hover:scale-105;
  }

  /* Section Spacing */
  .section-padding {
    @apply py-16 md:py-24 lg:py-32;
  }

  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  /* Text Contrast Utilities */
  .text-high-contrast {
    color: hsl(var(--text-primary));
  }

  .text-medium-contrast {
    color: hsl(var(--text-secondary));
  }

  .text-readable {
    color: hsl(var(--text-muted));
  }

  .text-subtle {
    color: hsl(var(--text-light));
  }

  /* Enhanced Glass Effect with Better Contrast */
  .glass-dark {
    @apply bg-gray-900/80 backdrop-blur-md border border-gray-700/20 shadow-lg text-white;
  }

  /* Better Card Contrast */
  .card-contrast {
    @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200;
  }
}

@layer utilities {
  /* Custom Scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) hsl(var(--muted));
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--foreground));
  }

  /* Animation Utilities */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
  }
}

/* Custom Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Typography */
@media (max-width: 640px) {
  h1 {
    @apply text-3xl;
  }

  h2 {
    @apply text-2xl;
  }

  h3 {
    @apply text-xl;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
}