
import { DollarSign, BookOpen, Plane, Users, Globe, Briefcase, FileCheck, Award, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

// Define static services data with icons
const staticServices = [
  {
    id: '1',
    title: 'University Selection',
    description: 'Expert guidance in choosing the right university based on your profile and aspirations.',
    icon: <Globe className="w-6 h-6" />
  },
  {
    id: '2',
    title: 'Admission Guidance',
    description: 'Comprehensive support throughout the university application process.',
    icon: <FileCheck className="w-6 h-6" />
  },
  {
    id: '3',
    title: 'Visa Assistance',
    description: 'Professional help with visa applications and documentation.',
    icon: <Award className="w-6 h-6" />
  },
  {
    id: '4',
    title: 'Scholarship Assistance',
    description: 'Guidance in finding and applying for scholarships and financial aid.',
    icon: <DollarSign className="w-6 h-6" />
  },
  {
    id: '5',
    title: 'Test Preparation',
    description: 'Resources and coaching for language and entrance exams.',
    icon: <BookOpen className="w-6 h-6" />
  },
  {
    id: '6',
    title: 'Career Counseling',
    description: 'Personalized career guidance to help you make informed decisions.',
    icon: <Briefcase className="w-6 h-6" />
  },
  {
    id: '7',
    title: 'Pre-departure Briefing',
    description: 'Essential information and tips before you leave for your studies.',
    icon: <Plane className="w-6 h-6" />
  },
  {
    id: '8',
    title: 'Post-arrival Support',
    description: 'Assistance with settling in and adjusting to your new environment.',
    icon: <Users className="w-6 h-6" />
  }
];

const ServicesOverview = () => {
  // Using static services instead of fetching from API
  const services = staticServices;
  const loading = false;
  const error = null;

  const gradientClasses = [
    'bg-gradient-to-br from-blue-100 to-blue-200 text-blue-700',
    'bg-gradient-to-br from-green-100 to-green-200 text-green-700',
    'bg-gradient-to-br from-purple-100 to-purple-200 text-purple-700',
    'bg-gradient-to-br from-orange-100 to-orange-200 text-orange-700',
    'bg-gradient-to-br from-indigo-100 to-indigo-200 text-indigo-700',
    'bg-gradient-to-br from-teal-100 to-teal-200 text-teal-700',
    'bg-gradient-to-br from-pink-100 to-pink-200 text-pink-700',
    'bg-gradient-to-br from-yellow-100 to-yellow-200 text-yellow-700'
  ];

  return (
    <section className="section-padding bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto container-padding">
        {/* Enhanced Header */}
        <div className="text-center mb-20 animate-fade-in">
          <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6">
            <Award className="h-4 w-4 mr-2" />
            Comprehensive Support
          </div>
          <h2 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Study Abroad
            <span className="text-gradient block">Services</span>
          </h2>
          <p className="text-xl md:text-2xl text-gray-700 max-w-4xl mx-auto leading-relaxed font-medium">
            From initial counseling to settling in your destination country, we provide
            <span className="font-bold text-blue-600"> end-to-end support</span> throughout your study abroad journey.
          </p>
        </div>

        {/* Enhanced Services Grid */}
        {loading && (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        )}
        {error && (
          <div className="text-center py-20">
            <p className="text-red-500 text-lg">Error: {error}</p>
          </div>
        )}
        {!loading && !error && services.length === 0 && (
          <div className="text-center py-20">
            <p className="text-gray-600 text-lg">No services found.</p>
          </div>
        )}
        {!loading && !error && services.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {services.map((service, index) => (
              <div
                key={service.id}
                className="card-elevated p-8 group hover-lift animate-slide-up"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <div className={`inline-flex p-4 rounded-2xl ${gradientClasses[index % gradientClasses.length]} mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg`}>
                  {service.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors">
                  {service.title}
                </h3>
                <p className="text-gray-700 leading-relaxed mb-4 font-medium">
                  {service.description}
                </p>
                <div className="flex items-center text-blue-600 font-medium text-sm group-hover:text-blue-700 transition-colors">
                  <span>Learn more</span>
                  <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Enhanced CTA Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-blue-600 to-orange-500 rounded-3xl p-12 text-white relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-4 left-4 w-8 h-8 bg-white rounded-full"></div>
              <div className="absolute top-8 right-8 w-6 h-6 bg-white rotate-45"></div>
              <div className="absolute bottom-8 left-8 w-4 h-4 bg-white rounded-full"></div>
              <div className="absolute bottom-4 right-4 w-10 h-10 bg-white rounded-full"></div>
            </div>

            <div className="relative z-10">
              <h3 className="text-3xl md:text-4xl font-bold mb-4">
                Ready to Start Your Journey?
              </h3>
              <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
                Discover all our services and take the first step towards your international education dream.
              </p>
              <Link
                to="/services"
                className="btn-secondary group inline-flex items-center"
              >
                View All Services
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesOverview;
