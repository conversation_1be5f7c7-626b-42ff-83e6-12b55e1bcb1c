import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Upload, 
  File, 
  Image, 
  X, 
  Check, 
  AlertCircle, 
  Loader2,
  FileText,
  FileImage,
  FileVideo,
  FileAudio
} from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface FileUploadProps {
  bucket: string;
  path?: string;
  accept?: Record<string, string[]>;
  maxSize?: number;
  maxFiles?: number;
  onUploadComplete?: (urls: string[]) => void;
  onUploadError?: (error: string) => void;
  className?: string;
  disabled?: boolean;
}

interface UploadFile {
  id: string;
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  url?: string;
  error?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  bucket,
  path = '',
  accept = {
    'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
    'application/pdf': ['.pdf'],
    'application/msword': ['.doc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
  },
  maxSize = 10 * 1024 * 1024, // 10MB
  maxFiles = 5,
  onUploadComplete,
  onUploadError,
  className = '',
  disabled = false
}) => {
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const { toast } = useToast();

  const getFileIcon = (file: File) => {
    const type = file.type;
    if (type.startsWith('image/')) return <FileImage className="h-8 w-8 text-blue-500" />;
    if (type.startsWith('video/')) return <FileVideo className="h-8 w-8 text-purple-500" />;
    if (type.startsWith('audio/')) return <FileAudio className="h-8 w-8 text-green-500" />;
    if (type === 'application/pdf') return <FileText className="h-8 w-8 text-red-500" />;
    return <File className="h-8 w-8 text-gray-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const generateFileName = (file: File) => {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = file.name.split('.').pop();
    return `${timestamp}_${randomString}.${extension}`;
  };

  const uploadFile = async (uploadFile: UploadFile): Promise<string> => {
    const fileName = generateFileName(uploadFile.file);
    const filePath = path ? `${path}/${fileName}` : fileName;

    return new Promise((resolve, reject) => {
      const upload = supabase.storage
        .from(bucket)
        .upload(filePath, uploadFile.file, {
          cacheControl: '3600',
          upsert: false
        });

      upload.then(({ data, error }) => {
        if (error) {
          reject(error);
        } else {
          const { data: urlData } = supabase.storage
            .from(bucket)
            .getPublicUrl(filePath);
          resolve(urlData.publicUrl);
        }
      }).catch(reject);
    });
  };

  const handleUpload = async () => {
    if (files.length === 0) return;

    setIsUploading(true);
    const uploadedUrls: string[] = [];
    const updatedFiles = [...files];

    try {
      for (let i = 0; i < updatedFiles.length; i++) {
        const file = updatedFiles[i];
        if (file.status !== 'pending') continue;

        // Update status to uploading
        updatedFiles[i] = { ...file, status: 'uploading', progress: 0 };
        setFiles([...updatedFiles]);

        try {
          // Simulate progress updates
          const progressInterval = setInterval(() => {
            updatedFiles[i] = { 
              ...updatedFiles[i], 
              progress: Math.min(updatedFiles[i].progress + 10, 90) 
            };
            setFiles([...updatedFiles]);
          }, 200);

          const url = await uploadFile(file);
          clearInterval(progressInterval);

          // Update to success
          updatedFiles[i] = { 
            ...file, 
            status: 'success', 
            progress: 100, 
            url 
          };
          uploadedUrls.push(url);
          setFiles([...updatedFiles]);

        } catch (error: any) {
          // Update to error
          updatedFiles[i] = { 
            ...file, 
            status: 'error', 
            progress: 0, 
            error: error.message 
          };
          setFiles([...updatedFiles]);
        }
      }

      if (uploadedUrls.length > 0) {
        onUploadComplete?.(uploadedUrls);
        toast({
          title: "Upload Successful",
          description: `${uploadedUrls.length} file(s) uploaded successfully.`,
        });
      }

    } catch (error: any) {
      onUploadError?.(error.message);
      toast({
        title: "Upload Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(({ file, errors }) => 
        `${file.name}: ${errors.map((e: any) => e.message).join(', ')}`
      );
      toast({
        title: "Some files were rejected",
        description: errors.join('\n'),
        variant: "destructive",
      });
    }

    // Add accepted files
    const newFiles: UploadFile[] = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(2, 15),
      file,
      progress: 0,
      status: 'pending'
    }));

    setFiles(prev => {
      const combined = [...prev, ...newFiles];
      if (combined.length > maxFiles) {
        toast({
          title: "Too many files",
          description: `Maximum ${maxFiles} files allowed. Extra files were removed.`,
          variant: "destructive",
        });
        return combined.slice(0, maxFiles);
      }
      return combined;
    });
  }, [maxFiles, toast]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept,
    maxSize,
    maxFiles,
    disabled: disabled || isUploading
  });

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id));
  };

  const clearAll = () => {
    setFiles([]);
  };

  const hasSuccessfulUploads = files.some(f => f.status === 'success');
  const hasPendingFiles = files.some(f => f.status === 'pending');

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Dropzone */}
      <Card>
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
              ${disabled || isUploading ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            <input {...getInputProps()} />
            <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            {isDragActive ? (
              <p className="text-blue-600 font-medium">Drop the files here...</p>
            ) : (
              <div>
                <p className="text-gray-600 font-medium mb-2">
                  Drag & drop files here, or click to select
                </p>
                <p className="text-sm text-gray-500">
                  Max {maxFiles} files, up to {formatFileSize(maxSize)} each
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  Supported: {Object.values(accept).flat().join(', ')}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* File List */}
      {files.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-medium">Files ({files.length})</h3>
              <div className="space-x-2">
                {hasPendingFiles && (
                  <Button 
                    onClick={handleUpload} 
                    disabled={isUploading}
                    size="sm"
                  >
                    {isUploading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    Upload All
                  </Button>
                )}
                <Button 
                  variant="outline" 
                  onClick={clearAll}
                  disabled={isUploading}
                  size="sm"
                >
                  Clear All
                </Button>
              </div>
            </div>

            <div className="space-y-3">
              {files.map((file) => (
                <div key={file.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                  {getFileIcon(file.file)}
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium truncate">{file.file.name}</p>
                      <div className="flex items-center space-x-2">
                        {file.status === 'success' && (
                          <Badge variant="secondary" className="text-green-600">
                            <Check className="h-3 w-3 mr-1" />
                            Uploaded
                          </Badge>
                        )}
                        {file.status === 'error' && (
                          <Badge variant="destructive">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            Failed
                          </Badge>
                        )}
                        {file.status === 'uploading' && (
                          <Badge variant="outline">
                            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                            Uploading
                          </Badge>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(file.id)}
                          disabled={isUploading}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.file.size)}
                    </p>
                    
                    {file.status === 'uploading' && (
                      <Progress value={file.progress} className="mt-2" />
                    )}
                    
                    {file.status === 'error' && file.error && (
                      <p className="text-xs text-red-500 mt-1">{file.error}</p>
                    )}
                    
                    {file.status === 'success' && file.url && (
                      <div className="mt-2">
                        <a 
                          href={file.url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-xs text-blue-500 hover:underline"
                        >
                          View uploaded file
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Summary */}
      {hasSuccessfulUploads && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Check className="h-5 w-5 text-green-600" />
              <p className="text-sm text-green-800">
                {files.filter(f => f.status === 'success').length} file(s) uploaded successfully
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FileUpload;
