import React, { useState, useEffect } from 'react';
import AdminLayout from '@/components/AdminLayout';
import { supabase } from '@/lib/supabase';
import { Country } from '@/hooks/useDestinations'; // Assuming useDestinations hook defines Country type

const AdminCountries: React.FC = () => {
  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddEditModal, setShowAddEditModal] = useState(false);
  const [currentCountry, setCurrentCountry] = useState<Country | null>(null);

  useEffect(() => {
    fetchCountries();
  }, []);

  const fetchCountries = async () => {
    try {
      const { data, error } = await supabase
        .from('countries')
        .select('*')
        .order('name', { ascending: true });

      if (error) {
        throw error;
      }
      setCountries(data || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this country?')) {
      try {
        const { error } = await supabase
          .from('countries')
          .delete()
          .eq('id', id);

        if (error) {
          throw error;
        }
        fetchCountries(); // Refresh the list after deletion
      } catch (err: any) {
        setError(err.message);
      }
    }
  };

  const handleAddEdit = (country?: Country) => {
    setCurrentCountry(country || null);
    setShowAddEditModal(true);
  };

  const handleSave = async (countryData: Omit<Country, 'id' | 'created_at'>) => {
    try {
      if (currentCountry) {
        // Update existing country
        const { error } = await supabase
          .from('countries')
          .update(countryData)
          .eq('id', currentCountry.id);

        if (error) throw error;
      } else {
        // Add new country
        const { error } = await supabase
          .from('countries')
          .insert(countryData);

        if (error) throw error;
      }
      setShowAddEditModal(false);
      fetchCountries(); // Refresh the list after save
    } catch (err: any) {
      setError(err.message);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="container mx-auto p-4">
          <h1 className="text-3xl font-bold mb-6">Manage Countries</h1>
          <p>Loading countries...</p>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="container mx-auto p-4">
          <h1 className="text-3xl font-bold mb-6">Manage Countries</h1>
          <p className="text-red-500">Error: {error}</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="container mx-auto p-4">
        <h1 className="text-3xl font-bold mb-6">Manage Countries</h1>
        <button
          onClick={() => handleAddEdit()}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mb-4"
        >
          Add New Country
        </button>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white shadow-md rounded-lg overflow-hidden">
            <thead className="bg-gray-200 text-gray-700">
              <tr>
                <th className="py-3 px-4 text-left">Name</th>
                <th className="py-3 px-4 text-left">Description</th>
                <th className="py-3 px-4 text-left">Image URL</th>
                <th className="py-3 px-4 text-left">Actions</th>
              </tr>
            </thead>
            <tbody className="text-gray-600">
              {countries.map((country) => (
                <tr key={country.id} className="border-b border-gray-200">
                  <td className="py-3 px-4">{country.name}</td>
                  <td className="py-3 px-4">{country.description?.substring(0, 100)}...</td>
                  <td className="py-3 px-4">
                    {country.image_url ? (
                      <img src={country.image_url} alt="flag" className="h-8 w-8 object-cover" />
                    ) : (
                      'N/A'
                    )}
                  </td>
                  <td className="py-3 px-4">
                    <button
                      onClick={() => handleAddEdit(country)}
                      className="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-sm mr-2"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDelete(country.id)}
                      className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-sm"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {showAddEditModal && (
          <CountryAddEditModal
            country={currentCountry}
            onSave={handleSave}
            onClose={() => setShowAddEditModal(false)}
          />
        )}
      </div>
    </AdminLayout>
  );
};

interface CountryAddEditModalProps {
  country: Country | null;
  onSave: (countryData: Omit<Country, 'id' | 'created_at'>) => void;
  onClose: () => void;
}

const CountryAddEditModal: React.FC<CountryAddEditModalProps> = ({ country, onSave, onClose }) => {
  const [name, setName] = useState(country?.name || '');
  const [description, setDescription] = useState(country?.description || '');
  const [imageUrl, setImageUrl] = useState(country?.image_url || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({ name, description, image_url: imageUrl });
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex justify-center items-center">
      <div className="bg-white p-8 rounded-lg shadow-xl w-1/2">
        <h2 className="text-2xl font-bold mb-4">{country ? 'Edit Country' : 'Add New Country'}</h2>
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="name">
              Name
            </label>
            <input
              type="text"
              id="name"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="description">
              Description
            </label>
            <textarea
              id="description"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={4}
            ></textarea>
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="imageUrl">
              Image URL
            </label>
            <input
              type="text"
              id="imageUrl"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
            />
          </div>
          <div className="flex items-center justify-between">
            <button
              type="submit"
              className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              Save
            </button>
            <button
              type="button"
              onClick={onClose}
              className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AdminCountries;