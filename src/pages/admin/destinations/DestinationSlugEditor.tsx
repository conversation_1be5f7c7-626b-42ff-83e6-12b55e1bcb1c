import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { ArrowLeft, Save } from 'lucide-react';

interface Country {
  id: string;
  name: string;
  slug: string;
  flag_url: string;
}

const DestinationSlugEditor = () => {
  const { countryId } = useParams<{ countryId: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [country, setCountry] = useState<Country>({
    id: '',
    name: '',
    slug: '',
    flag_url: ''
  });

  // Fetch country data
  useEffect(() => {
    const fetchCountry = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('countries')
          .select('*')
          .eq('id', countryId)
          .single();

        if (error) throw error;
        if (data) setCountry(data);
      } catch (error) {
        console.error('Error fetching country:', error);
        toast.error('Failed to load country data');
        navigate('/admin/destinations');
      } finally {
        setLoading(false);
      }
    };

    if (countryId) {
      fetchCountry();
    }
  }, [countryId, navigate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCountry(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w-]+/g, '');
  };

  const handleGenerateSlug = () => {
    if (country.name) {
      setCountry(prev => ({
        ...prev,
        slug: generateSlug(prev.name)
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!country.slug) {
      toast.error('Please enter a slug');
      return;
    }

    try {
      setSaving(true);

      const { error } = await supabase
        .from('countries')
        .update({ slug: country.slug })
        .eq('id', countryId);

      if (error) throw error;

      toast.success('Slug updated successfully');
      navigate('/admin/destinations');
    } catch (error) {
      console.error('Error updating slug:', error);
      toast.error('Failed to update slug');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-navy-900"></div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <div className="flex items-center mb-6">
        <button
          onClick={() => navigate('/admin/destinations')}
          className="flex items-center text-navy-700 hover:text-navy-900 mr-4"
        >
          <ArrowLeft className="w-5 h-5 mr-1" />
          Back to Destinations
        </button>
        <h1 className="text-2xl font-bold text-navy-900">
          Edit Destination Slug
        </h1>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-2">{country.name}</h2>
          {country.flag_url && (
            <img
              src={country.flag_url}
              alt={country.name}
              className="h-12 w-16 object-cover rounded border border-gray-200"
            />
          )}
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-1">
              URL Slug
            </label>
            <div className="flex rounded-md shadow-sm">
              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                /destinations/
              </span>
              <input
                type="text"
                id="slug"
                name="slug"
                value={country.slug}
                onChange={handleInputChange}
                className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none border border-gray-300 focus:outline-none focus:ring-navy-500 focus:border-navy-500"
                placeholder="e.g., south-korea"
              />
            </div>
            <div className="mt-2">
              <button
                type="button"
                onClick={handleGenerateSlug}
                className="text-sm text-navy-600 hover:text-navy-800"
              >
                Generate from name
              </button>
              <p className="mt-1 text-xs text-gray-500">
                The slug is used in the URL. Only use lowercase letters, numbers, and hyphens.
              </p>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={() => navigate('/admin/destinations')}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-navy-600 hover:bg-navy-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-navy-500 disabled:opacity-50"
            >
              {saving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-1" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DestinationSlugEditor;
