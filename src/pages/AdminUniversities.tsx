import React, { useState, useEffect } from 'react';
import AdminLayout from '@/components/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import FileUpload from '@/components/FileUpload';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Star, 
  Globe, 
  MapPin, 
  Users, 
  GraduationCap,
  Loader2,
  ExternalLink,
  Building,
  Award
} from 'lucide-react';
import { Database } from '@/types/database.types';

type University = Database['public']['Tables']['universities']['Row'];
type UniversityInsert = Database['public']['Tables']['universities']['Insert'];
type UniversityUpdate = Database['public']['Tables']['universities']['Update'];
type Country = Database['public']['Tables']['countries']['Row'];
type Course = Database['public']['Tables']['courses']['Row'];

const AdminUniversities: React.FC = () => {
  const [universities, setUniversities] = useState<University[]>([]);
  const [countries, setCountries] = useState<Country[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddEditModal, setShowAddEditModal] = useState(false);
  const [currentUniversity, setCurrentUniversity] = useState<University | null>(null);
  const [formData, setFormData] = useState<Partial<UniversityInsert>>({});
  const [saving, setSaving] = useState(false);
  const [selectedTab, setSelectedTab] = useState('basic');
  const { toast } = useToast();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch universities with country information
      const { data: universitiesData, error: universitiesError } = await supabase
        .from('universities')
        .select(`
          *,
          countries (
            id,
            name,
            code,
            flag_url
          )
        `)
        .order('name', { ascending: true });

      if (universitiesError) throw universitiesError;

      // Fetch countries for dropdown
      const { data: countriesData, error: countriesError } = await supabase
        .from('countries')
        .select('*')
        .order('name', { ascending: true });

      if (countriesError) throw countriesError;

      // Fetch courses for this university
      const { data: coursesData, error: coursesError } = await supabase
        .from('courses')
        .select('*')
        .order('name', { ascending: true });

      if (coursesError) throw coursesError;

      setUniversities(universitiesData || []);
      setCountries(countriesData || []);
      setCourses(coursesData || []);
    } catch (err: any) {
      setError(err.message);
      toast({
        title: "Error",
        description: "Failed to fetch data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this university? This will also delete all associated courses.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('universities')
        .delete()
        .eq('id', id);

      if (error) throw error;
      
      toast({
        title: "Success",
        description: "University deleted successfully",
      });
      fetchData();
    } catch (err: any) {
      toast({
        title: "Error",
        description: "Failed to delete university",
        variant: "destructive",
      });
    }
  };

  const handleAddEdit = (university?: University) => {
    setCurrentUniversity(university || null);
    setFormData(university ? {
      name: university.name,
      description: university.description,
      country_id: university.country_id,
      logo_url: university.logo_url,
      website_url: university.website_url,
      address: university.address,
      city: university.city,
      state: university.state,
      postal_code: university.postal_code,
      phone: university.phone,
      email: university.email,
      established_year: university.established_year,
      student_count: university.student_count,
      international_students: university.international_students,
      acceptance_rate: university.acceptance_rate,
      ranking_national: university.ranking_national,
      ranking_global: university.ranking_global,
      tuition_domestic: university.tuition_domestic,
      tuition_international: university.tuition_international,
      application_fee: university.application_fee,
      application_deadline: university.application_deadline,
      is_featured: university.is_featured,
      is_partner: university.is_partner
    } : {
      name: '',
      description: '',
      country_id: '',
      logo_url: '',
      website_url: '',
      address: '',
      city: '',
      state: '',
      postal_code: '',
      phone: '',
      email: '',
      established_year: null,
      student_count: null,
      international_students: null,
      acceptance_rate: null,
      ranking_national: null,
      ranking_global: null,
      tuition_domestic: null,
      tuition_international: null,
      application_fee: null,
      application_deadline: '',
      is_featured: false,
      is_partner: false
    });
    setSelectedTab('basic');
    setShowAddEditModal(true);
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      if (!formData.name || !formData.country_id) {
        toast({
          title: "Error",
          description: "University name and country are required",
          variant: "destructive",
        });
        return;
      }

      const slug = formData.name?.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
      const universityData = { ...formData, slug };

      if (currentUniversity) {
        const { error } = await supabase
          .from('universities')
          .update(universityData)
          .eq('id', currentUniversity.id);

        if (error) throw error;
        toast({
          title: "Success",
          description: "University updated successfully",
        });
      } else {
        const { error } = await supabase
          .from('universities')
          .insert(universityData);

        if (error) throw error;
        toast({
          title: "Success",
          description: "University created successfully",
        });
      }
      
      setShowAddEditModal(false);
      setCurrentUniversity(null);
      setFormData({});
      fetchData();
    } catch (err: any) {
      toast({
        title: "Error",
        description: "Failed to save university",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleLogoUpload = (urls: string[]) => {
    if (urls.length > 0) {
      setFormData({ ...formData, logo_url: urls[0] });
      toast({
        title: "Success",
        description: "Logo uploaded successfully",
      });
    }
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <AdminLayout>
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Universities Management</h1>
              <p className="text-gray-600">Manage university partners and their information</p>
            </div>
            <Dialog open={showAddEditModal} onOpenChange={setShowAddEditModal}>
              <DialogTrigger asChild>
                <Button onClick={() => handleAddEdit()}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add University
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>
                    {currentUniversity ? 'Edit University' : 'Add New University'}
                  </DialogTitle>
                  <DialogDescription>
                    {currentUniversity ? 'Update the university details below.' : 'Create a new university profile.'}
                  </DialogDescription>
                </DialogHeader>
                
                <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="basic">Basic Info</TabsTrigger>
                    <TabsTrigger value="contact">Contact</TabsTrigger>
                    <TabsTrigger value="stats">Statistics</TabsTrigger>
                    <TabsTrigger value="financial">Financial</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="basic" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">University Name *</Label>
                        <Input
                          id="name"
                          value={formData.name || ''}
                          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                          placeholder="Enter university name"
                        />
                      </div>
                      <div>
                        <Label htmlFor="country_id">Country *</Label>
                        <Select
                          value={formData.country_id || ''}
                          onValueChange={(value) => setFormData({ ...formData, country_id: value })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select country" />
                          </SelectTrigger>
                          <SelectContent>
                            {countries.map((country) => (
                              <SelectItem key={country.id} value={country.id}>
                                {country.flag_url && (
                                  <img src={country.flag_url} alt="" className="inline w-4 h-3 mr-2" />
                                )}
                                {country.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={formData.description || ''}
                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                        placeholder="Enter university description"
                        rows={4}
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="website_url">Website URL</Label>
                        <Input
                          id="website_url"
                          value={formData.website_url || ''}
                          onChange={(e) => setFormData({ ...formData, website_url: e.target.value })}
                          placeholder="https://university.edu"
                        />
                      </div>
                      <div>
                        <Label htmlFor="established_year">Established Year</Label>
                        <Input
                          id="established_year"
                          type="number"
                          value={formData.established_year || ''}
                          onChange={(e) => setFormData({ ...formData, established_year: parseInt(e.target.value) })}
                          placeholder="1950"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label>University Logo</Label>
                      <FileUpload
                        bucket="university_logos"
                        path="logos"
                        accept={{ 'image/*': ['.png', '.jpg', '.jpeg', '.svg'] }}
                        maxFiles={1}
                        maxSize={2 * 1024 * 1024} // 2MB
                        onUploadComplete={handleLogoUpload}
                        className="mt-2"
                      />
                      {formData.logo_url && (
                        <div className="mt-2">
                          <img src={formData.logo_url} alt="Logo preview" className="h-16 w-16 object-contain" />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="is_featured"
                          checked={formData.is_featured || false}
                          onCheckedChange={(checked) => setFormData({ ...formData, is_featured: checked })}
                        />
                        <Label htmlFor="is_featured">Featured University</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="is_partner"
                          checked={formData.is_partner || false}
                          onCheckedChange={(checked) => setFormData({ ...formData, is_partner: checked })}
                        />
                        <Label htmlFor="is_partner">Partner University</Label>
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="contact" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email || ''}
                          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div>
                        <Label htmlFor="phone">Phone</Label>
                        <Input
                          id="phone"
                          value={formData.phone || ''}
                          onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                          placeholder="+****************"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="address">Address</Label>
                      <Input
                        id="address"
                        value={formData.address || ''}
                        onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                        placeholder="123 University Ave"
                      />
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="city">City</Label>
                        <Input
                          id="city"
                          value={formData.city || ''}
                          onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                          placeholder="City"
                        />
                      </div>
                      <div>
                        <Label htmlFor="state">State/Province</Label>
                        <Input
                          id="state"
                          value={formData.state || ''}
                          onChange={(e) => setFormData({ ...formData, state: e.target.value })}
                          placeholder="State"
                        />
                      </div>
                      <div>
                        <Label htmlFor="postal_code">Postal Code</Label>
                        <Input
                          id="postal_code"
                          value={formData.postal_code || ''}
                          onChange={(e) => setFormData({ ...formData, postal_code: e.target.value })}
                          placeholder="12345"
                        />
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="stats" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="student_count">Total Students</Label>
                        <Input
                          id="student_count"
                          type="number"
                          value={formData.student_count || ''}
                          onChange={(e) => setFormData({ ...formData, student_count: parseInt(e.target.value) })}
                          placeholder="25000"
                        />
                      </div>
                      <div>
                        <Label htmlFor="international_students">International Students</Label>
                        <Input
                          id="international_students"
                          type="number"
                          value={formData.international_students || ''}
                          onChange={(e) => setFormData({ ...formData, international_students: parseInt(e.target.value) })}
                          placeholder="5000"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="acceptance_rate">Acceptance Rate (%)</Label>
                        <Input
                          id="acceptance_rate"
                          type="number"
                          step="0.1"
                          value={formData.acceptance_rate || ''}
                          onChange={(e) => setFormData({ ...formData, acceptance_rate: parseFloat(e.target.value) })}
                          placeholder="65.5"
                        />
                      </div>
                      <div>
                        <Label htmlFor="ranking_national">National Ranking</Label>
                        <Input
                          id="ranking_national"
                          type="number"
                          value={formData.ranking_national || ''}
                          onChange={(e) => setFormData({ ...formData, ranking_national: parseInt(e.target.value) })}
                          placeholder="25"
                        />
                      </div>
                      <div>
                        <Label htmlFor="ranking_global">Global Ranking</Label>
                        <Input
                          id="ranking_global"
                          type="number"
                          value={formData.ranking_global || ''}
                          onChange={(e) => setFormData({ ...formData, ranking_global: parseInt(e.target.value) })}
                          placeholder="150"
                        />
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="financial" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="tuition_domestic">Domestic Tuition (Annual)</Label>
                        <Input
                          id="tuition_domestic"
                          type="number"
                          value={formData.tuition_domestic || ''}
                          onChange={(e) => setFormData({ ...formData, tuition_domestic: parseFloat(e.target.value) })}
                          placeholder="15000"
                        />
                      </div>
                      <div>
                        <Label htmlFor="tuition_international">International Tuition (Annual)</Label>
                        <Input
                          id="tuition_international"
                          type="number"
                          value={formData.tuition_international || ''}
                          onChange={(e) => setFormData({ ...formData, tuition_international: parseFloat(e.target.value) })}
                          placeholder="35000"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="application_fee">Application Fee</Label>
                        <Input
                          id="application_fee"
                          type="number"
                          value={formData.application_fee || ''}
                          onChange={(e) => setFormData({ ...formData, application_fee: parseFloat(e.target.value) })}
                          placeholder="100"
                        />
                      </div>
                      <div>
                        <Label htmlFor="application_deadline">Application Deadline</Label>
                        <Input
                          id="application_deadline"
                          value={formData.application_deadline || ''}
                          onChange={(e) => setFormData({ ...formData, application_deadline: e.target.value })}
                          placeholder="March 1st"
                        />
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
                
                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowAddEditModal(false);
                      setCurrentUniversity(null);
                      setFormData({});
                    }}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleSave} disabled={saving}>
                    {saving && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    {currentUniversity ? 'Update' : 'Create'} University
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Universities Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {universities.map((university) => (
              <Card key={university.id} className="relative">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      {university.logo_url ? (
                        <img
                          src={university.logo_url}
                          alt={university.name}
                          className="h-12 w-12 object-contain"
                        />
                      ) : (
                        <div className="h-12 w-12 rounded bg-gray-200 flex items-center justify-center">
                          <Building className="h-6 w-6 text-gray-500" />
                        </div>
                      )}
                      <div>
                        <CardTitle className="text-lg">{university.name}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          {university.is_featured && (
                            <Badge variant="secondary">
                              <Star className="h-3 w-3 mr-1" />
                              Featured
                            </Badge>
                          )}
                          {university.is_partner && (
                            <Badge variant="outline">
                              <Award className="h-3 w-3 mr-1" />
                              Partner
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <CardDescription className="line-clamp-2">
                      {university.description || 'No description available'}
                    </CardDescription>
                    
                    <div className="text-sm text-gray-500 space-y-1">
                      {university.countries && (
                        <div className="flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          {university.countries.name}
                        </div>
                      )}
                      {university.student_count && (
                        <div className="flex items-center">
                          <Users className="h-3 w-3 mr-1" />
                          {university.student_count.toLocaleString()} students
                        </div>
                      )}
                      {university.ranking_global && (
                        <div className="flex items-center">
                          <GraduationCap className="h-3 w-3 mr-1" />
                          Global Rank: #{university.ranking_global}
                        </div>
                      )}
                      {university.website_url && (
                        <div className="flex items-center">
                          <Globe className="h-3 w-3 mr-1" />
                          <a 
                            href={university.website_url} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-blue-500 hover:underline flex items-center"
                          >
                            Visit Website
                            <ExternalLink className="h-3 w-3 ml-1" />
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex justify-end space-x-2 mt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAddEdit(university)}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(university.id)}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {universities.length === 0 && !loading && (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <div className="text-center">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No universities found</h3>
                  <p className="text-gray-500 mb-4">Get started by adding your first university partner.</p>
                  <Button onClick={() => handleAddEdit()}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add University
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
};

export default AdminUniversities;
