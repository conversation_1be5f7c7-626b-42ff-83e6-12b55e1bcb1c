import React, { useState, useEffect } from 'react';
import AdminLayout from '@/components/AdminLayout';
import { supabase } from '@/lib/supabase';
import { BlogPost } from '@/hooks/useBlogPosts'; // Assuming a useBlogPosts hook exists and defines BlogPost type

const AdminBlogPosts: React.FC = () => {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddEditModal, setShowAddEditModal] = useState(false);
  const [currentBlogPost, setCurrentBlogPost] = useState<BlogPost | null>(null);

  useEffect(() => {
    fetchBlogPosts();
  }, []);

  const fetchBlogPosts = async () => {
    try {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .order('published_at', { ascending: false });

      if (error) {
        throw error;
      }
      setBlogPosts(data || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this blog post?')) {
      try {
        const { error } = await supabase
          .from('blog_posts')
          .delete()
          .eq('id', id);

        if (error) {
          throw error;
        }
        fetchBlogPosts(); // Refresh the list after deletion
      } catch (err: any) {
        setError(err.message);
      }
    }
  };

  const handleAddEdit = (blogPost?: BlogPost) => {
    setCurrentBlogPost(blogPost || null);
    setShowAddEditModal(true);
  };

  const handleSave = async (blogPostData: Omit<BlogPost, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      if (currentBlogPost) {
        // Update existing blog post
        const { error } = await supabase
          .from('blog_posts')
          .update(blogPostData)
          .eq('id', currentBlogPost.id);

        if (error) throw error;
      } else {
        // Add new blog post
        const { error } = await supabase
          .from('blog_posts')
          .insert(blogPostData);

        if (error) throw error;
      }
      setShowAddEditModal(false);
      fetchBlogPosts(); // Refresh the list after save
    } catch (err: any) {
      setError(err.message);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="container mx-auto p-4">
          <h1 className="text-3xl font-bold mb-6">Manage Blog Posts</h1>
          <p>Loading blog posts...</p>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="container mx-auto p-4">
          <h1 className="text-3xl font-bold mb-6">Manage Blog Posts</h1>
          <p className="text-red-500">Error: {error}</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="container mx-auto p-4">
        <h1 className="text-3xl font-bold mb-6">Manage Blog Posts</h1>
        <button
          onClick={() => handleAddEdit()}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mb-4"
        >
          Add New Blog Post
        </button>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white shadow-md rounded-lg overflow-hidden">
            <thead className="bg-gray-200 text-gray-700">
              <tr>
                <th className="py-3 px-4 text-left">Title</th>
                <th className="py-3 px-4 text-left">Author</th>
                <th className="py-3 px-4 text-left">Published At</th>
                <th className="py-3 px-4 text-left">Actions</th>
              </tr>
            </thead>
            <tbody className="text-gray-600">
              {blogPosts.map((post) => (
                <tr key={post.id} className="border-b border-gray-200">
                  <td className="py-3 px-4">{post.title}</td>
                  <td className="py-3 px-4">{post.author}</td>
                  <td className="py-3 px-4">{post.published_at ? new Date(post.published_at).toLocaleDateString() : 'N/A'}</td>
                  <td className="py-3 px-4">
                    <button
                      onClick={() => handleAddEdit(post)}
                      className="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-sm mr-2"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDelete(post.id)}
                      className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-sm"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {showAddEditModal && (
          <BlogPostAddEditModal
            blogPost={currentBlogPost}
            onSave={handleSave}
            onClose={() => setShowAddEditModal(false)}
          />
        )}
      </div>
    </AdminLayout>
  );
};

interface BlogPostAddEditModalProps {
  blogPost: BlogPost | null;
  onSave: (blogPostData: Omit<BlogPost, 'id' | 'created_at' | 'updated_at'>) => void;
  onClose: () => void;
}

const BlogPostAddEditModal: React.FC<BlogPostAddEditModalProps> = ({ blogPost, onSave, onClose }) => {
  const [title, setTitle] = useState(blogPost?.title || '');
  const [slug, setSlug] = useState(blogPost?.slug || '');
  const [content, setContent] = useState(blogPost?.content || '');
  const [author, setAuthor] = useState(blogPost?.author || '');
  const [publishedAt, setPublishedAt] = useState(blogPost?.published_at ? new Date(blogPost.published_at).toISOString().split('T')[0] : '');
  const [imageUrl, setImageUrl] = useState(blogPost?.image_url || '');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({ title, slug, content, author, published_at: publishedAt ? new Date(publishedAt).toISOString() : null, image_url: imageUrl });
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex justify-center items-center">
      <div className="bg-white p-8 rounded-lg shadow-xl w-1/2">
        <h2 className="text-2xl font-bold mb-4">{blogPost ? 'Edit Blog Post' : 'Add New Blog Post'}</h2>
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="title">
              Title
            </label>
            <input
              type="text"
              id="title"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              required
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="slug">
              Slug
            </label>
            <input
              type="text"
              id="slug"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={slug}
              onChange={(e) => setSlug(e.target.value)}
              required
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="content">
              Content
            </label>
            <textarea
              id="content"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              rows={8}
              required
            ></textarea>
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="author">
              Author
            </label>
            <input
              type="text"
              id="author"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={author}
              onChange={(e) => setAuthor(e.target.value)}
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="publishedAt">
              Published At
            </label>
            <input
              type="date"
              id="publishedAt"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={publishedAt}
              onChange={(e) => setPublishedAt(e.target.value)}
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="imageUrl">
              Image URL
            </label>
            <input
              type="text"
              id="imageUrl"
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
            />
          </div>
          <div className="flex items-center justify-between">
            <button
              type="submit"
              className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              Save
            </button>
            <button
              type="button"
              onClick={onClose}
              className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AdminBlogPosts;